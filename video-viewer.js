// RealWear Video Viewer JavaScript

// 返回功能
function goBack() {
    console.log('Going back...');
    // 这里可以添加返回上一页面的逻辑
    alert('返回上一页面');
}

// 播放视频功能
function playVideo() {
    console.log('Playing video...');
    // 这里可以添加播放视频的逻辑
    const playButton = document.querySelector('.play-button');
    
    if (playButton.textContent === 'PLAY') {
        playButton.textContent = 'PAUSE';
        playButton.style.backgroundColor = '#ff9800';
        alert('开始播放视频');
    } else {
        playButton.textContent = 'PLAY';
        playButton.style.backgroundColor = '#666';
        alert('暂停播放视频');
    }
}

// 删除视频功能
function deleteVideo() {
    console.log('Deleting video...');
    // 这里可以添加删除确认对话框
    const confirmed = confirm('确定要删除这个视频吗？');
    
    if (confirmed) {
        alert('视频已删除');
        // 这里可以添加实际的删除逻辑
    }
}

// 更新时间显示
function updateTime() {
    const timeElement = document.querySelector('.current-time');
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
    timeElement.textContent = timeString;
}

// 键盘快捷键支持
document.addEventListener('keydown', function(event) {
    switch(event.key) {
        case 'Escape':
        case 'Backspace':
            goBack();
            break;
        case ' ':
        case 'Enter':
            event.preventDefault();
            playVideo();
            break;
        case 'Delete':
            deleteVideo();
            break;
    }
});

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 每秒更新时间
    setInterval(updateTime, 1000);
    updateTime(); // 立即更新一次
    
    console.log('RealWear Video Viewer initialized');
});

// 模拟步骤进度更新
function updateStepProgress(currentStep, totalSteps) {
    const stepElement = document.querySelector('.step-info');
    stepElement.textContent = `Step ${currentStep} of ${totalSteps}`;
}

// 测试功能：模拟步骤切换
function nextStep() {
    const stepElement = document.querySelector('.step-info');
    const currentText = stepElement.textContent;
    const match = currentText.match(/Step (\d+) of (\d+)/);
    
    if (match) {
        const current = parseInt(match[1]);
        const total = parseInt(match[2]);
        
        if (current < total) {
            updateStepProgress(current + 1, total);
        }
    }
}

function previousStep() {
    const stepElement = document.querySelector('.step-info');
    const currentText = stepElement.textContent;
    const match = currentText.match(/Step (\d+) of (\d+)/);
    
    if (match) {
        const current = parseInt(match[1]);
        const total = parseInt(match[2]);
        
        if (current > 1) {
            updateStepProgress(current - 1, total);
        }
    }
}
