<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RealWear Smart Glass UI Designer</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="design-environment">
        <!-- Design Toolbar -->
        <div class="toolbar">
            <h1>RealWear UI Designer</h1>
            <div class="device-info">
                <span>Resolution: 854 x 480</span>
                <span>FOV: 20°</span>
            </div>
            <div class="controls">
                <button id="toggleGrid">Show Grid</button>
                <button id="toggleRuler">Show Ruler</button>
                <button id="resetZoom">Reset Zoom</button>
                <select id="themeSelector">
                    <option value="light">Light Theme</option>
                    <option value="dark">Dark Theme</option>
                    <option value="industrial">Industrial Theme</option>
                </select>
            </div>
        </div>

        <!-- 主设计区域 -->
        <div class="design-area">
            <!-- RealWear设备模拟器 -->
            <div class="device-simulator">
                <div class="device-frame">
                    <div class="screen" id="realwearScreen">
                        <!-- Video Viewer Interface -->
                        <div class="ui-container">
                            <!-- Header with task info and time -->
                            <div class="video-header">
                                <div class="header-left">
                                    <span class="demo-title">Testing (demo1)</span>
                                </div>
                                <div class="header-center">
                                    <span class="current-time">09:28 AM</span>
                                </div>
                                <div class="header-right">
                                    <span class="step-text">Step 2 of 3</span>
                                </div>
                            </div>

                            <!-- Video Display Area -->
                            <div class="video-display-area">
                                <div class="video-frame">
                                    <img src="sample.png" alt="Video Content" class="video-content">
                                </div>
                            </div>

                            <!-- Control Buttons -->
                            <div class="video-controls">
                                <button class="control-btn back-btn" onclick="goBack()">BACK</button>
                                <button class="control-btn play-btn" onclick="playVideo()">PLAY</button>
                                <button class="control-btn delete-btn" onclick="deleteVideo()">DELETE</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Component Panel -->
            <div class="component-panel">
                <h3>UI Components</h3>
                <div class="component-list">
                    <div class="component-item" data-component="button">Button</div>
                    <div class="component-item" data-component="menu">Menu</div>
                    <div class="component-item" data-component="card">Card</div>
                    <div class="component-item" data-component="list">List</div>
                    <div class="component-item" data-component="form">Form</div>
                    <div class="component-item" data-component="qr">QR Code</div>
                </div>
            </div>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel">
            <h3>Properties</h3>
            <div class="property-group">
                <label>Background Color:</label>
                <input type="color" id="bgColor" value="#000000">
            </div>
            <div class="property-group">
                <label>Text Color:</label>
                <input type="color" id="textColor" value="#ffffff">
            </div>
            <div class="property-group">
                <label>Font Size:</label>
                <input type="range" id="fontSize" min="12" max="24" value="16">
                <span id="fontSizeValue">16px</span>
            </div>
            <div class="property-group">
                <label>Padding:</label>
                <input type="range" id="padding" min="0" max="20" value="10">
                <span id="paddingValue">10px</span>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
