/* RealWear Smart Glass UI Designer Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f0f0f0;
    overflow: hidden;
}

.design-environment {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* 工具栏样式 */
.toolbar {
    background: #2c3e50;
    color: white;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.toolbar h1 {
    font-size: 18px;
    margin: 0;
}

.device-info {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #bdc3c7;
}

.controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.controls button, .controls select {
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    background: #34495e;
    color: white;
    cursor: pointer;
    font-size: 12px;
}

.controls button:hover {
    background: #4a6741;
}

/* 主设计区域 */
.design-area {
    display: flex;
    flex: 1;
    background: #ecf0f1;
}

/* RealWear设备模拟器 */
.device-simulator {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
}

.device-frame {
    background: #2c3e50;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    position: relative;
}

.device-frame::before {
    content: 'RealWear Navigator';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    color: #bdc3c7;
    font-size: 10px;
    font-weight: bold;
}

/* RealWear屏幕 - 精确的854x480分辨率 */
.screen {
    width: 854px;
    height: 480px;
    background: #000;
    border-radius: 5px;
    overflow: hidden;
    position: relative;
    transform: scale(0.8); /* 缩放以适应屏幕 */
    transform-origin: center;
}

/* UI容器 */
.ui-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    color: white;
    font-family: 'Arial', sans-serif;
    background: #000;
}

/* Header section */
.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #333;
}

.system-title {
    font-size: 18px;
    font-weight: bold;
    color: white;
    margin: 0;
}

.settings-icon {
    font-size: 20px;
    cursor: pointer;
}

/* Login section */
.login-section {
    flex: 1;
    display: flex;
    padding: 40px;
    gap: 60px;
    align-items: center;
}

.left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 60px;
}

.right-panel {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Company logo section */
.company-logo {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo-image {
    width: 190px;
    height: 190px;
    object-fit: contain;
}

.company-text {
    display: flex;
    flex-direction: column;
}

.company-name {
    font-size: 32px;
    font-weight: bold;
    color: white;
    letter-spacing: 1px;
}

.company-subtitle {
    font-size: 16px;
    color: #888;
    letter-spacing: 2px;
}

/* Login title section */
.login-title {
    text-align: left;
}

.login-title h2 {
    font-size: 48px;
    font-weight: bold;
    color: white;
    margin: 0 0 8px 0;
    line-height: 1;
}

.login-title p {
    font-size: 18px;
    color: #888;
    margin: 0;
}



/* Video Header */
.video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #1e1e2e;
    border-bottom: 1px solid #313244;
    height: 60px;
}

.header-left .demo-title {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
}

.header-center .current-time {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
}

.header-right .step-text {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.welcome-text {
    color: #cdd6f4;
    font-size: 16px;
    font-weight: 500;
}

.network-status {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-end;
}

.upload-rate, .download-rate {
    display: flex;
    align-items: center;
    gap: 6px;
}

.upload-icon {
    color: #f38ba8;
    font-size: 12px;
    font-weight: bold;
}

.download-icon {
    color: #a6e3a1;
    font-size: 12px;
    font-weight: bold;
}

.rate-text {
    color: #a6adc8;
    font-size: 12px;
    font-weight: 500;
    min-width: 60px;
    text-align: right;
}

/* Task Dashboard */
.task-dashboard {
    display: flex;
    height: calc(100% - 60px);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: #181825;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 30px;
    border-right: 1px solid #313244;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-placeholder {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #313244;
    border-radius: 8px;
    color: #a6adc8;
    font-size: 12px;
    font-weight: 500;
}

.sidebar-logo-text {
    display: flex;
    flex-direction: column;
}

.sidebar-company-name {
    font-size: 18px;
    font-weight: bold;
    color: #cdd6f4;
    letter-spacing: 1px;
}

.sidebar-company-subtitle {
    font-size: 10px;
    color: #a6adc8;
    letter-spacing: 2px;
}

/* Task Statistics */
.task-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-label {
    color: #a6adc8;
    font-size: 12px;
}

.stat-value {
    color: #cdd6f4;
    font-size: 24px;
    font-weight: bold;
}





/* Content Area */
.content-area {
    flex: 1;
    padding: 20px;
    background: #11111b;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Video Display Area */
.video-display-area {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #11111b;
    padding: 30px;
}

.video-frame {
    width: 100%;
    max-width: 600px;
    height: 300px;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.video-content {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Video Controls */
.video-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    padding: 20px;
    background: #11111b;
    height: 80px;
}

.control-btn {
    padding: 12px 30px;
    font-size: 16px;
    font-weight: bold;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.back-btn {
    background-color: #4CAF50;
    color: white;
}

.back-btn:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.play-btn {
    background-color: #666;
    color: white;
}

.play-btn:hover {
    background-color: #777;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 102, 102, 0.3);
}

.delete-btn {
    background-color: #f44336;
    color: white;
}

.delete-btn:hover {
    background-color: #da190b;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.control-btn:active {
    transform: translateY(0);
}

/* 关闭相机确认对话框样式 */
.close-dialog {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.dialog-content {
    background-color: rgba(40, 40, 40, 0.95);
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    max-width: 500px;
    width: 80%;
    border: 1px solid #555;
}

.dialog-title {
    color: white;
    font-size: 28px;
    font-weight: bold;
    margin: 0 0 20px 0;
}

.dialog-text {
    color: white;
    font-size: 18px;
    margin: 10px 0;
    line-height: 1.4;
}

.dialog-hint {
    color: #ccc;
    font-size: 16px;
    margin: 20px 0 30px 0;
    font-style: italic;
}

.dialog-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.dialog-button {
    padding: 12px 30px;
    font-size: 16px;
    font-weight: bold;
    border: 2px solid;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.skip-button {
    background-color: transparent;
    color: #4CAF50;
    border-color: #4CAF50;
}

.skip-button:hover {
    background-color: #4CAF50;
    color: white;
}

.return-button {
    background-color: transparent;
    color: #2196F3;
    border-color: #2196F3;
}

.return-button:hover {
    background-color: #2196F3;
    color: white;
}



.camera-view {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
    overflow: visible;
    height: calc(100vh - 160px);
    min-height: 300px;
}

.camera-background {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.stop-button-container {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
}

.stop-button {
    background: rgba(108, 117, 125, 0.9);
    color: #ffffff;
    border: 2px solid #6c757d;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 100px;
}

.stop-button:hover {
    background: rgba(108, 117, 125, 1);
    border-color: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.stop-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}



/* Error Banner */
.error-banner {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-radius: 8px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-left: 3px solid #dc3545;
    box-shadow: 0 3px 8px rgba(220, 53, 69, 0.4);
    margin-top: auto;
    margin-bottom: 5px;
}

.error-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.error-message {
    color: white;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.2;
    flex: 1;
}

/* Task List */
.task-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
}

.task-item {
    background: #1e1e2e;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #89b4fa;
    transition: all 0.3s ease;
}

.task-item:hover {
    background: #262637;
    border-left-color: #74c7ec;
}

.task-name {
    color: #cdd6f4;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
}

.task-progress {
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #313244;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #89b4fa, #74c7ec);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    color: #a6adc8;
    font-size: 14px;
    min-width: 30px;
}

.progress-percent {
    color: #a6adc8;
    font-size: 14px;
    min-width: 35px;
    text-align: right;
}





/* 组件面板 */
.component-panel {
    width: 200px;
    background: white;
    border-left: 1px solid #ddd;
    padding: 20px;
    overflow-y: auto;
}

.component-panel h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 14px;
}

.component-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.component-item {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.component-item:hover {
    background: #e9ecef;
    border-color: #3498db;
}

/* 属性面板 */
.properties-panel {
    width: 250px;
    background: white;
    border-left: 1px solid #ddd;
    padding: 20px;
    overflow-y: auto;
}

.properties-panel h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 14px;
}

.property-group {
    margin-bottom: 15px;
}

.property-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #555;
}

.property-group input {
    width: 100%;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.property-group input[type="range"] {
    margin-bottom: 5px;
}

/* 主题样式 */
.theme-dark .screen {
    background: #1a1a1a;
}

.theme-light .screen {
    background: #ffffff;
    color: #333;
}

.theme-light .status-bar {
    background: rgba(0,0,0,0.1);
    color: #333;
}

.theme-industrial .screen {
    background: #2c3e50;
}

.theme-industrial .menu-item {
    border-left-color: #f39c12;
}

.theme-industrial .menu-number {
    background: #f39c12;
}

/* 网格显示 */
.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background-image: 
        linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .screen {
        transform: scale(0.6);
    }
}

@media (max-width: 1200px) {
    .screen {
        transform: scale(0.5);
    }
}
